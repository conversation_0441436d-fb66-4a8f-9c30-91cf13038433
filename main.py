"""
Main entry point for the conversation analysis agent.
"""
import os
import asyncio
import argparse
from typing import Dict, Any, List
from dotenv import load_dotenv
from concurrent.futures import TimeoutError
import random

from src.file_handler import get_input_files, read_text_file, save_result, save_text_result, aggregate_results
from src.parallel_manager import <PERSON>lle<PERSON><PERSON><PERSON><PERSON>
from src.token_counter import TokenCounter
from src.result_formatter import format_individual_result
from src.md_aggregator import initialize_md_file, update_md_file
from src.analyzers import UnifiedAnalyzer


async def process_file(file_path: str, output_path: str) -> Dict[str, Any]:
    """
    Process a single file through the analysis pipeline.

    Args:
        file_path: Path to the input file
        output_path: Path to save the result

    Returns:
        Dictionary with processing result status and details
    """
    file_name = os.path.basename(file_path)
    print(f"[{file_name}] Starting file processing")

    try:
        # Read the file
        print(f"[{file_name}] Reading file content from path: {file_path}")
        if not os.path.exists(file_path):
            print(f"[{file_name}] ERROR: File not found at specified path")
            return {"status": "failed", "file_name": file_name, "error": "File not found"}
        text = read_text_file(file_path)
        print(f"[{file_name}] File read successfully, content length: {len(text)} characters")

        # Initialize token counter
        token_counter = TokenCounter()

        # Initialize unified analyzer
        print(f"[{file_name}] Initializing unified analyzer")
        unified_analyzer = UnifiedAnalyzer(token_counter=token_counter)

        # Run unified analysis
        print(f"[{file_name}] Running unified analysis with text length: {len(text)}")
        if len(text.strip()) == 0:
            print(f"[{file_name}] ERROR: Empty file content")
            return {"status": "failed", "file_name": file_name, "error": "Empty file content"}
        # Get task timeout from environment variable
        task_timeout = int(os.environ.get("TASK_TIMEOUT", 300))  # Default to 5 minutes
        
        # Add timeout and retry mechanism for two-stage analysis
        # Enhanced retry mechanism with exponential backoff
        max_retries = 3
        retry_count = 0
        analysis_result = None
        initial_delay = 1  # Initial delay in seconds
        
        while retry_count <= max_retries and analysis_result is None:
            try:
                # Set timeout for the entire analysis process (initial + review)
                analysis_result = await asyncio.wait_for(
                    unified_analyzer.analyze(text),
                    timeout=task_timeout  # Use environment variable for timeout
                )
                
                # Check if analysis produced valid result
                if not analysis_result or "error" in analysis_result:
                    raise Exception(f"Analysis failed: {analysis_result.get('error', 'Unknown error')}")
                    
            except (TimeoutError, Exception) as e:
                retry_count += 1
                if retry_count > max_retries:
                    raise
                    
                print(f"[{file_name}] Analysis attempt {retry_count} failed: {str(e)}. Retrying...")
                
                # Exponential backoff with jitter
                delay = initial_delay * (2 ** retry_count) * (0.5 + random.random() * 0.5)
                print(f"[{file_name}] Retrying in {delay:.2f} seconds...")
                await asyncio.sleep(delay)
        print(f"[{file_name}] Unified analysis completed")

        # Check for timeout or error in analysis result
        if "error" in analysis_result and isinstance(analysis_result["error"], str):
            error_msg = analysis_result["error"].lower()
            if "timeout" in error_msg or "error: llm response" in error_msg or "error: " in error_msg:
                raise TimeoutError(f"LLM response timeout during unified analysis: {analysis_result['error']}")

        # Extract individual results from the unified result
        data_cleaning_result = analysis_result.get("data_cleaning", {})
        sentiment_result = analysis_result.get("sentiment_analysis", {})
        resistance_result = analysis_result.get("resistance_points", {})
        competitive_result = analysis_result.get("competitive_products", {})
        keyword_result = analysis_result.get("keywords", {})

        # Get token usage
        token_usage = token_counter.get_usage()
        print(f"[{file_name}] Total token usage: {token_usage['total']}")

        # Format the result
        print(f"[{file_name}] Formatting results")
        result = format_individual_result(
            file_path,
            data_cleaning_result,
            sentiment_result,
            resistance_result,
            competitive_result,
            keyword_result,
            token_usage
        )

        # Save the result
        print(f"[{file_name}] Saving JSON result to {output_path}")
        print(f"[DEBUG] Output directory writeable: {os.access(os.path.dirname(output_path), os.W_OK)}")
        save_result(result, output_path)

        # Save text result
        # Update All_result.md
        output_dir = os.path.dirname(output_path)
        print(f"[{file_name}] Updating All_result.md")
        update_md_file(result, output_dir)

        print(f"[{file_name}] File processing completed successfully")
        return {"status": "success", "file_name": file_name, "result": result}

    except TimeoutError as e:
        # Handle timeout error
        print(f"[{file_name}] LLM response timeout: {str(e)}")

        # Mark file as failed in All_result.md
        output_dir = os.path.dirname(output_path)
        from src.md_aggregator import mark_file_as_failed
        mark_file_as_failed(file_name, str(e), output_dir)

        return {"status": "failed", "file_name": file_name, "error": str(e)}

    except Exception as e:
        # Handle other errors
        print(f"[{file_name}] Error processing file: {str(e)}")
        import traceback
        print(f"[{file_name}] Traceback: {traceback.format_exc()}")

        # Mark file as failed in All_result.md
        output_dir = os.path.dirname(output_path)
        from src.md_aggregator import mark_file_as_failed
        mark_file_as_failed(file_name, str(e), output_dir)

        return {"status": "failed", "file_name": file_name, "error": str(e)}


async def main():
    """
    Main function for the conversation analysis agent.
    """
    # Load environment variables
    load_dotenv()

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Conversation Analysis Agent")
    parser.add_argument("--input-dir", type=str, default="input", help="Input directory")
    parser.add_argument("--output-dir", type=str, default="output", help="Output directory")
    parser.add_argument("--max-files", type=int, default=None, help="Maximum number of files to process")
    parser.add_argument("--continue", action="store_true", help="Continue from the last failed file")
    parser.add_argument("--continue-from", type=str, help="Continue from a specific file")
    parser.add_argument("--rate-limit", type=int, default=int(os.environ.get("API_RATE_LIMIT", 800)),
                        help="API rate limit (requests per minute)")
    parser.add_argument("--time-window", type=int, default=int(os.environ.get("API_TIME_WINDOW", 60)),
                        help="API rate limit time window in seconds")
    parser.add_argument("--max-workers", type=int, default=None, help="Maximum number of workers for parallel processing")
    args = parser.parse_args()

    # Configure rate limiting for the LLM client
    os.environ["API_RATE_LIMIT"] = str(args.rate_limit)
    os.environ["API_TIME_WINDOW"] = str(args.time_window)

    print(f"API rate limit configured: {args.rate_limit} requests per {args.time_window} seconds")

    # Get input files
    files = get_input_files(args.input_dir, args.max_files)
    print(f"[DEBUG] Found {len(files)} files to process: {[os.path.basename(f) for f in files]}")

    if not files:
        print(f"No input files found in {args.input_dir}")
        return

    print(f"Found {len(files)} input files")

    # Create output directory if it doesn't exist
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)

    # Initialize All_result.md if it doesn't exist
    md_path = os.path.join(args.output_dir, "All_result.md")
    if not os.path.exists(md_path):
        initialize_md_file(args.output_dir)

    # Handle continue mode
    if getattr(args, 'continue') or args.continue_from:
        # Get the file to continue from
        continue_from_file = args.continue_from

        if not continue_from_file and getattr(args, 'continue'):
            # Find the last failed file from All_result.md
            from src.md_aggregator import get_failed_files
            failed_files = get_failed_files(args.output_dir)

            if failed_files:
                # Get the first failed file
                continue_from_file = failed_files[0]
                print(f"Continuing from the first failed file: {continue_from_file}")
            else:
                print("No failed files found. Processing all files.")

        if continue_from_file:
            # Find the index of the file to continue from
            continue_from_path = None
            for i, file_path in enumerate(files):
                if os.path.basename(file_path) == continue_from_file:
                    continue_from_path = file_path
                    break

            if continue_from_path:
                # Get the index of the file to continue from
                start_index = files.index(continue_from_path)
                # Slice the files list to start from the continue_from file
                files = files[start_index:]
                print(f"Continuing from file {continue_from_file} ({len(files)} files remaining)")
            else:
                print(f"File {continue_from_file} not found in input directory. Processing all files.")

    # Initialize parallel manager
    parallel_manager = ParallelManager()

    # Process files in parallel
    print(f"[DEBUG] Starting parallel processing with {args.max_workers} workers")
    result_files = await parallel_manager.process_files_parallel(
        files,
        process_file,
        args.output_dir
    )

    # Filter out failed files
    successful_result_files = [f for f in result_files if f is not None]

    # Check if we have any successful files to aggregate
    if successful_result_files:
        # Aggregate results
        aggregated_result_path = os.path.join(args.output_dir, "All_result.json")
        aggregated_result = aggregate_results(successful_result_files, aggregated_result_path)

        print(f"Processed {len(result_files)} files")
        print(f"Successfully processed {len(successful_result_files)} files")
        print(f"Failed to process {len(result_files) - len(successful_result_files)} files")
        print(f"Total token usage: {aggregated_result['token_usage']['total']}")
        print(f"Results saved to {args.output_dir}")
    else:
        print("No files were successfully processed.")

    # If there were failures, print a message about how to continue
    if len(result_files) - len(successful_result_files) > 0:
        print("\nSome files failed to process. To continue from the failed files, run:")
        print(f"python main.py --continue")
        print("Or to continue from a specific file:")
        print(f"python main.py --continue-from <filename>")


if __name__ == "__main__":
    asyncio.run(main())
