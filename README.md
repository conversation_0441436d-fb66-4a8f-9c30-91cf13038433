# Conversation Analysis Agent

A LangChain-based agent for analyzing automotive sales conversations using the Qwen-Turbo-2025-04-28 model.

## Features

- Reads conversation text files from the input directory
- Processes files in parallel for efficiency
- Performs multiple analyses on each conversation:
  - Data cleaning and target vehicle identification
  - Customer sentiment analysis
  - Customer resistance point analysis
  - Competitive product mention analysis
  - Keyword statistics
- Tracks token usage for each analysis
- Outputs structured results in both JSON and text formats
- Aggregates results across all processed files

## Project Structure

```
autoshow_analysis_langchain_v2/
├── .env                      # API keys and configuration
├── main.py                   # Entry point
├── input/                    # Input text files
├── output/                   # Output results
├── prompts/                  # Prompt templates
│   ├── data_cleaning.md
│   ├── sentiment_analysis.md
│   ├── resistance_points.md
│   ├── competitive_products.md
│   └── keywords.md
├── src/
│   ├── file_handler.py       # File reading/writing utilities
│   ├── parallel_manager.py   # Parallel workflow management
│   ├── token_counter.py      # Token usage tracking
│   ├── result_formatter.py   # Result formatting utilities
│   └── analyzers/
│       ├── __init__.py
│       ├── base_analyzer.py  # Base class for analyzers
│       ├── data_cleaner.py
│       ├── sentiment_analyzer.py
│       ├── resistance_analyzer.py
│       ├── competitive_analyzer.py
│       └── keyword_analyzer.py
└── requirements.txt          # Project dependencies
```

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file with your API key:
   ```
   DASHSCOPE_API_KEY=your_api_key_here
   MAX_PARALLEL_PROCESSES=3
   ```
4. Place your conversation text files in the `input` directory

## Usage

Run the analysis with default settings:
```
python main.py
```

Customize the execution:
```
python main.py --input-dir input --output-dir output --max-files 5 --max-workers 3
```

## Output

The agent produces two types of output for each processed file:
1. A JSON file with structured analysis results
2. A text file with a human-readable summary

Additionally, it creates an aggregated result file (`All_result.json`) that combines the results from all processed files.

## Customization

You can customize the analysis by modifying the prompt templates in the `prompts` directory. Each template is a markdown file that defines the instructions for a specific analysis task.
