"""
Test script for LLM API connections.
Tests both Qwen API and local model configurations.
"""
import asyncio
import os
import argparse
from dotenv import load_dotenv
from src.llm_client import LocalLLMClient

# Load environment variables
load_dotenv()

async def test_api(provider: str = None):
    """
    Test the API connection.

    Args:
        provider: The provider to test ("qwen" or "local"). If None, uses the default from .env
    """
    print(f"Testing API connection for provider: {provider or os.environ.get('MODEL_PROVIDER', 'qwen')}...")

    # Create client with specified provider
    client = LocalLLMClient(provider=provider)

    print(f"Provider: {client.provider}")
    print(f"API Base: {client.api_base}")
    print(f"Model: {client.model}")

    # Check if API key is loaded (for Qwen API)
    if client.provider == "qwen" and client.api_key:
        print(f"API Key loaded: True")
        print(f"API Key starts with: {client.api_key[:5]}...")

    test_prompt = "你好，请用中文回答：今天天气怎么样？"
    print(f"Sending test prompt: {test_prompt}")

    try:
        response = await client.generate(test_prompt)
        print(f"Response status code: {response.status_code}")

        if response.status_code == 200:
            if hasattr(response.output, 'choices'):
                content = response.output.choices[0].message.content
            else:
                content = response.output['choices'][0]['message']['content']
            print(f"Response content: {content}")
        else:
            print(f"Error: {response.output}")
    except Exception as e:
        print(f"Exception: {e}")

async def test_both_providers():
    """Test both Qwen API and local model."""
    print("=== Testing Qwen API ===")
    await test_api("qwen")

    print("\n=== Testing Local Model ===")
    await test_api("local")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test LLM API connections")
    parser.add_argument("--provider", type=str, choices=["qwen", "local", "both"],
                        default=None, help="Provider to test (qwen, local, or both)")
    args = parser.parse_args()

    if args.provider == "both":
        asyncio.run(test_both_providers())
    else:
        asyncio.run(test_api(args.provider))
