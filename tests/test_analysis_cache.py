import unittest
import time
from src.cache.analysis_cache import AnalysisCache

class TestAnalysisCache(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures"""
        # 保存原始环境变量以便测试后恢复
        import os
        self.original_env = {
            'CACHE_SIZE': os.getenv('CACHE_SIZE'),
            'CACHE_TTL': os.getenv('CACHE_TTL')
        }
        
        # 设置测试环境变量
        os.environ['CACHE_SIZE'] = '100'
        os.environ['CACHE_TTL'] = '10'  # 10秒过期
        
        self.cache = AnalysisCache()
        self.test_text = "测试文本内容"
        self.test_params = {"param1": "value1", "param2": 2}
        self.test_result = {"analysis": "测试结果", "score": 0.95}
    
    def tearDown(self):
        """Restore original environment variables"""
        import os
        for key, value in self.original_env.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value
    
    def test_cache_hit(self):
        """Test that cached results are returned on cache hit"""
        # 首先缓存结果
        self.cache.cache_result(self.test_text, self.test_result, **self.test_params)
        
        # 再次请求相同内容
        cached_result = self.cache.get_cached_result(self.test_text, **self.test_params)
        
        # 验证结果
        self.assertIsNotNone(cached_result)
        self.assertEqual(cached_result, self.test_result)
    
    def test_cache_miss(self):
        """Test that None is returned on cache miss"""
        # 请求未缓存的内容
        result = self.cache.get_cached_result("未缓存的文本", **self.test_params)
        
        # 验证结果
        self.assertIsNone(result)
    
    def test_cache_expiration(self):
        """Test that cache entries expire after TTL"""
        # 缓存结果
        self.cache.cache_result(self.test_text, self.test_result, **self.test_params)
        
        # 等待缓存过期
        time.sleep(11)  # TTL设置为10秒，等待11秒确保过期
        
        # 尝试获取已过期的缓存
        result = self.cache.get_cached_result(self.test_text, **self.test_params)
        
        # 验证缓存已过期
        self.assertIsNone(result)
    
    def test_cache_key_generation(self):
        """Test that different inputs generate different cache keys"""
        # 生成不同输入的缓存键
        key1 = self.cache._generate_cache_key(self.test_text, **self.test_params)
        key2 = self.cache._generate_cache_key("不同的文本", **self.test_params)
        key3 = self.cache._generate_cache_key(self.test_text, param1="不同的值", param2=2)
        
        # 验证所有键都不同
        self.assertNotEqual(key1, key2)
        self.assertNotEqual(key1, key3)
        self.assertNotEqual(key2, key3)
    
    def test_cache_clear_specific(self):
        """Test clearing a specific cache entry"""
        # 缓存结果
        self.cache.cache_result(self.test_text, self.test_result, **self.test_params)
        
        # 清除特定缓存项
        cleared_count = self.cache.clear_cache(self.test_text, **self.test_params)
        
        # 验证缓存已清除
        self.assertEqual(cleared_count, 1)
        self.assertIsNone(self.cache.get_cached_result(self.test_text, **self.test_params))
    
    def test_cache_clear_all(self):
        """Test clearing all cache entries"""
        # 缓存多个结果
        self.cache.cache_result("文本1", {"result": "1"}, param="a")
        self.cache.cache_result("文本2", {"result": "2"}, param="b")
        self.cache.cache_result("文本3", {"result": "3"}, param="c")
        
        # 记录初始缓存大小
        initial_size = len(self.cache.cache)
        
        # 清除所有缓存
        cleared_count = self.cache.clear_cache()
        
        # 验证所有缓存已清除
        self.assertEqual(cleared_count, initial_size)
        self.assertEqual(len(self.cache.cache), 0)
    
    def test_cache_stats(self):
        """Test cache statistics collection"""
        # 获取初始统计信息
        stats = self.cache.get_stats()
        
        # 验证统计信息结构
        self.assertIn("current_size", stats)
        self.assertIn("max_size", stats)
        self.assertIn("ttl", stats)
        
        # 验证初始值
        self.assertEqual(stats["current_size"], 0)
        self.assertEqual(stats["max_size"], 100)
        self.assertEqual(stats["ttl"], 10)

if __name__ == '__main__':
    unittest.main()