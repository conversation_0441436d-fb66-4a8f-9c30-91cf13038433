from pydantic import BaseModel, Field
from typing import List

class SentimentDetail(BaseModel):
    """Schema for sentiment detail."""
    aspect: str = Field(description="The specific aspect of the vehicle or sales process")
    sentiment: str = Field(description="positive/negative/neutral")
    explanation: str = Field(description="Brief explanation of why this sentiment was detected")


class SentimentAnalysisOutput(BaseModel):
    """Schema for sentiment analysis output."""
    overall_sentiment: str = Field(description="Overall sentiment of the customer (positive/negative/neutral)")
    sentiment_details: List[SentimentDetail] = Field(description="Details of sentiment for specific aspects")
    summary: str = Field(description="Brief summary of the sentiment analysis")


class ResistancePoint(BaseModel):
    """Schema for resistance point."""
    point: str = Field(description="Brief description of the resistance point")
    category: str = Field(description="price/features/performance/reliability/etc.")
    severity: str = Field(description="high/medium/low")
    explanation: str = Field(description="Detailed explanation of the resistance point")


class ResistanceAnalysisOutput(BaseModel):
    """Schema for resistance analysis output."""
    points: List[ResistancePoint] = Field(description="List of resistance points")
    summary: str = Field(description="Brief summary of the resistance points")


class CompetitiveProduct(BaseModel):
    """Schema for competitive product."""
    model: str = Field(description="The model name")
    count: int = Field(description="Number of mentions")


class CompetitiveAnalysisOutput(BaseModel):
    """Schema for competitive analysis output."""
    products: List[CompetitiveProduct] = Field(description="List of competitive products mentioned")
    summary: str = Field(description="Brief summary of the competitive product mentions")


class Keyword(BaseModel):
    """Schema for keyword."""
    keyword: str = Field(description="The keyword or phrase")
    category: str = Field(description="salesman/customer/fixed word")
    count: int = Field(description="Number of mentions")
    importance: str = Field(description="high/medium/low")


class KeywordAnalysisOutput(BaseModel):
    """Schema for keyword analysis output."""
    keywords: List[Keyword] = Field(description="List of keywords")
    summary: str = Field(description="Brief summary of the key findings from keyword analysis")


class DataCleaningOutput(BaseModel):
    """Schema for data cleaning output."""
    target_vehicle: str = Field(description="The specific vehicle model the customer is primarily interested in")
    target_vehicle_aspects: str = Field(description="Key aspects the customer is interested in about the target vehicle")
    cleaned_text: str = Field(description="The cleaned conversation text", default="")


class UnifiedAnalysisOutput(BaseModel):
    """Schema for unified analysis output."""
    data_cleaning: DataCleaningOutput = Field(description="Data cleaning and target vehicle identification")
    sentiment_analysis: SentimentAnalysisOutput = Field(description="Sentiment analysis results")
    resistance_points: ResistanceAnalysisOutput = Field(description="Resistance points analysis results")
    competitive_products: CompetitiveAnalysisOutput = Field(description="Competitive products analysis results")
    keywords: KeywordAnalysisOutput = Field(description="Keyword analysis results")