"""
LLM client for the conversation analysis agent.
This module provides a client for interacting with LLM API endpoints.
"""
import os
import json
import asyncio
import time
from typing import Dict, Any, Optional, List, Deque
from collections import deque
import aiohttp


class RateLimiter:
    """
    Rate limiter for API requests to avoid hitting rate limits.
    Implements a token bucket algorithm to control request rate.
    """

    def __init__(self, rate_limit: int = 1000, time_window: int = 60, max_burst: int = 50):
        """
        Initialize the rate limiter.

        Args:
            rate_limit: Maximum number of requests allowed in the time window
            time_window: Time window in seconds
            max_burst: Maximum number of requests that can be made in a burst
        """
        self.rate_limit = rate_limit  # Requests per time window
        self.time_window = time_window  # Time window in seconds
        self.max_burst = max_burst  # Maximum burst size

        # Token bucket parameters
        self.tokens = max_burst  # Start with max tokens
        self.last_refill_time = time.time()

        # Request timestamps for sliding window rate limiting
        self.request_timestamps: Deque[float] = deque(maxlen=rate_limit)

        # Lock for thread safety
        self.lock = asyncio.Lock()

        print(f"[RateLimiter] Initialized with rate limit: {rate_limit} requests per {time_window} seconds")
        print(f"[RateLimiter] Maximum burst size: {max_burst} requests")

    async def acquire(self) -> float:
        """
        Acquire permission to make a request.
        If the rate limit would be exceeded, this method will wait until a request can be made.

        Returns:
            The wait time in seconds
        """
        async with self.lock:
            # Refill tokens based on elapsed time
            current_time = time.time()
            elapsed = current_time - self.last_refill_time

            # Calculate tokens to add based on elapsed time
            tokens_to_add = (elapsed / self.time_window) * self.rate_limit
            self.tokens = min(self.max_burst, self.tokens + tokens_to_add)
            self.last_refill_time = current_time

            # Check if we have enough tokens
            if self.tokens < 1:
                # Calculate wait time
                wait_time = (1 - self.tokens) * (self.time_window / self.rate_limit)
                print(f"[RateLimiter] Rate limit reached. Waiting for {wait_time:.2f} seconds")
                return wait_time

            # Use one token
            self.tokens -= 1

            # Also check sliding window rate limit
            if len(self.request_timestamps) >= self.rate_limit:
                # Check if oldest request is outside the time window
                oldest_request = self.request_timestamps[0]
                time_since_oldest = current_time - oldest_request

                if time_since_oldest < self.time_window:
                    # We're at the rate limit, calculate wait time
                    wait_time = self.time_window - time_since_oldest
                    print(f"[RateLimiter] Sliding window rate limit reached. Waiting for {wait_time:.2f} seconds")
                    return wait_time

                # Remove oldest request as it's outside the window
                self.request_timestamps.popleft()

            # Add current request timestamp
            self.request_timestamps.append(current_time)

            return 0  # No wait needed

    async def wait(self) -> None:
        """
        Wait if necessary to comply with the rate limit.
        """
        wait_time = await self.acquire()
        if wait_time > 0:
            print(f"[RateLimiter] Waiting for {wait_time:.2f} seconds to comply with rate limit")
            await asyncio.sleep(wait_time)


class LocalLLMClient:
    """
    Client for interacting with LLM API endpoints.
    Supports both Qwen API and local model deployments.
    """

    # Class-level rate limiter shared across all instances
    _rate_limiter = None

    def __init__(
        self,
        api_base: str = None,
        api_key: str = None,
        model: str = None,
        provider: str = None,
        rate_limit: int = 1000,
        time_window: int = 60
    ):
        """
        Initialize the LLM client.

        Args:
            api_base: Base URL for the API endpoint
            api_key: API key (may not be needed for local deployment)
            model: Model name to use
            provider: Provider to use ("qwen" or "local")
            rate_limit: Maximum number of requests allowed in the time window
            time_window: Time window in seconds
        """
        # Get provider from environment or use the provided one
        self.provider = provider or os.environ.get("MODEL_PROVIDER", "qwen")

        # Set up configuration based on provider
        if self.provider == "local":
            # Use local model configuration
            self.api_base = api_base or os.environ.get("LOCAL_API_BASE", "http://localhost:8000/v1")
            self.api_key = api_key  # Local deployment might not need an API key
            self.model = model or os.environ.get("LOCAL_MODEL", "Qwen3-4B")
        else:
            # Default to Qwen API
            self.api_base = api_base or os.environ.get("OPENAI_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
            self.api_key = api_key or os.environ.get("OPENAI_API_KEY", "")
            self.model = model or os.environ.get("OPENAI_MODEL", "qwen-turbo-latest")

        # Ensure the API base does not end with a slash for compatibility
        if self.api_base.endswith('/'):
            self.api_base = self.api_base[:-1]

        # Initialize rate limiter if not already initialized
        if LocalLLMClient._rate_limiter is None:
            LocalLLMClient._rate_limiter = RateLimiter(
                rate_limit=rate_limit,
                time_window=time_window,
                max_burst=min(50, rate_limit // 2)  # Set max burst to half the rate limit or 50, whichever is smaller
            )

        # Store reference to rate limiter
        self.rate_limiter = LocalLLMClient._rate_limiter

        print(f"[LocalLLMClient] Provider: {self.provider}")
        print(f"[LocalLLMClient] Initialized with API base: {self.api_base}")
        print(f"[LocalLLMClient] Using model: {self.model}")
        print(f"[LocalLLMClient] Rate limiting enabled: {rate_limit} requests per {time_window} seconds")

    async def generate(
        self,
        prompt: str,
        temperature: float = 0.3,
        max_tokens: int = 2048,
        stop: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Generate a response from the model.

        Args:
            prompt: The prompt to send to the model
            temperature: Temperature parameter for generation
            max_tokens: Maximum number of tokens to generate
            stop: Optional list of stop sequences

        Returns:
            Response from the model
        """
        headers = {
            "Content-Type": "application/json"
        }

        # Add API key to headers if provided
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        # Prepare the request payload
        payload = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        if stop:
            payload["stop"] = stop

        print(f"[LocalLLMClient] Prompt length: {len(prompt)} characters")

        # Apply rate limiting if using Qwen API
        if self.provider == "qwen":
            print(f"[LocalLLMClient] Applying rate limiting before API call")
            await self.rate_limiter.wait()
            print(f"[LocalLLMClient] Rate limit check passed, proceeding with API call")

        start_time = asyncio.get_event_loop().time()

        # Construct the URL for the API endpoint
        current_url = f"{self.api_base}/chat/completions"

        print(f"[LocalLLMClient] Sending request to {current_url}")

        try:
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.post(current_url, headers=headers, json=payload, timeout=400) as response:
                        status_code = response.status

                        end_time = asyncio.get_event_loop().time()
                        elapsed_time = end_time - start_time

                        print(f"[LocalLLMClient] Response received in {elapsed_time:.2f} seconds")
                        print(f"[LocalLLMClient] Response status: {status_code}")

                        # Handle different response types based on content type
                        content_type = response.headers.get('Content-Type', '')
                        print(f"[LocalLLMClient] Response content type: {content_type}")

                        # Try to get the response text first
                        response_text = await response.text()

                        # Parse JSON if possible
                        response_json = {}
                        if 'application/json' in content_type:
                            try:
                                response_json = json.loads(response_text)
                            except json.JSONDecodeError:
                                print(f"[LocalLLMClient] Failed to parse JSON response: {response_text[:200]}...")
                                response_json = {"error": {"message": "Invalid JSON response"}}
                        else:
                            # For non-JSON responses, create a structured error
                            if status_code != 200:
                                response_json = {"error": {"message": f"Server error: {response_text[:200]}..."}}
                            else:
                                # Try to parse as JSON anyway
                                try:
                                    response_json = json.loads(response_text)
                                except json.JSONDecodeError:
                                    # Create a mock JSON response
                                    response_json = {
                                        "choices": [
                                            {
                                                "message": {
                                                    "content": response_text
                                                }
                                            }
                                        ]
                                    }

                        # Create a response object similar to dashscope's response
                        result = {
                            "status_code": status_code,
                            "request": payload,
                            "output": response_json,
                            "usage": self._extract_usage(response_json)
                        }

                        # Handle error responses
                        if status_code != 200:
                            print(f"[LocalLLMClient] Error response: {response_json}")

                            # Create a standardized error response
                            error_message = response_json.get('error', {}).get('message', f'Server error: {status_code}')

                            # Create a mock response with choices for compatibility
                            mock_response = {
                                "choices": [
                                    {
                                        "message": {
                                            "content": f"Error: {error_message}"
                                        }
                                    }
                                ]
                            }
                            result["output"] = mock_response
                            print(f"[LocalLLMClient] Created mock response for error: {error_message}")

                        # Add content length debug info and clean up response if needed
                        if "choices" in result["output"] and len(result["output"]["choices"]) > 0:
                            content = result["output"]["choices"][0]["message"]["content"]

                            # Clean up the response if it contains <think> tags
                            if "<think>" in content and "</think>" in content:
                                # Extract content after </think>
                                cleaned_content = content.split("</think>", 1)[1].strip()
                                result["output"]["choices"][0]["message"]["content"] = cleaned_content
                                content = cleaned_content
                                print(f"[LocalLLMClient] Cleaned up <think> tags from response")

                            print(f"[LocalLLMClient] Response content length: {len(content)} characters")

                        return type('Response', (), result)

                except aiohttp.ClientError as e:
                    print(f"[LocalLLMClient] Network error: {str(e)}")
                    # Create a timeout error response
                    mock_result = {
                        "status_code": 500,
                        "request": payload,
                        "output": {
                            "choices": [
                                {
                                    "message": {
                                        "content": f"Error: LLM response timeout: {str(e)}"
                                    }
                                }
                            ]
                        },
                        "usage": self._extract_usage({"choices": [{"message": {"content": ""}}]})
                    }
                    return type('Response', (), mock_result)

        except Exception as e:
            print(f"[LocalLLMClient] Unexpected error: {str(e)}")
            import traceback
            print(f"[LocalLLMClient] Traceback: {traceback.format_exc()}")

            # Create an error response
            mock_result = {
                "status_code": 500,
                "request": payload,
                "output": {
                    "choices": [
                        {
                            "message": {
                                "content": f"Error: LLM response timeout: {str(e)}"
                            }
                        }
                    ]
                },
                "usage": self._extract_usage({"choices": [{"message": {"content": ""}}]})
            }
            return type('Response', (), mock_result)

    def _extract_usage(self, response_json: Dict[str, Any]) -> Any:
        """
        Extract token usage from the response.

        Args:
            response_json: Response JSON from the API

        Returns:
            Usage object with input_tokens and output_tokens attributes
        """
        usage = {}

        if "usage" in response_json:
            usage = {
                "input_tokens": response_json["usage"].get("prompt_tokens", 0),
                "output_tokens": response_json["usage"].get("completion_tokens", 0),
                "total_tokens": response_json["usage"].get("total_tokens", 0)
            }
        else:
            # If usage is not provided, estimate based on response length
            content_length = 0
            if "choices" in response_json and len(response_json["choices"]) > 0:
                content = response_json["choices"][0]["message"]["content"]
                content_length = len(content)

            # Rough estimate: 1 token ≈ 3 characters
            estimated_output_tokens = content_length // 3
            estimated_input_tokens = len(str(response_json.get("prompt", ""))) // 3

            usage = {
                "input_tokens": estimated_input_tokens,
                "output_tokens": estimated_output_tokens,
                "total_tokens": estimated_input_tokens + estimated_output_tokens
            }

        return type('Usage', (), usage)
