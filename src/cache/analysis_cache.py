import os
import time
import hashlib
import logging
import threading
from datetime import timedelta
from cachetools import TTL<PERSON>ache
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

class AnalysisCache:
    def __init__(self):
        # 从环境变量获取缓存配置，设置默认值
        self.cache_size = int(os.getenv('CACHE_SIZE', 1000))
        self.cache_ttl = int(os.getenv('CACHE_TTL', 3600))  # 默认1小时
        
        # 初始化TTLCache，支持LRU和过期时间
        self.cache = TTLCache(
            maxsize=self.cache_size,
            ttl=self.cache_ttl,
            timer=time.time  # 使用当前时间戳作为timer
        )
        
        # 线程锁确保线程安全
        self.lock = threading.Lock()
        
        logger.info(f"初始化分析缓存: 大小={self.cache_size}, 过期时间={self.cache_ttl}秒")
    
    def _generate_cache_key(self, text: str, **params) -> str:
        """基于输入文本和参数生成唯一缓存键"""
        # 组合文本和参数，确保参数顺序一致
        sorted_params = sorted(params.items())
        param_string = "&".join([f"{k}={v}" for k, v in sorted_params])
        combined = f"{text}|{param_string}".encode('utf-8')
        
        # 使用SHA-256生成哈希值作为缓存键
        return hashlib.sha256(combined).hexdigest()
    
    def get_cached_result(self, text: str, **params) -> dict:
        """获取缓存结果，如果未命中返回None"""
        key = self._generate_cache_key(text, **params)
        
        with self.lock:
            if key in self.cache:
                logger.debug(f"缓存命中: {key}")
                return self.cache[key]
        
        logger.debug(f"缓存未命中: {key}")
        return None
    
    def cache_result(self, text: str, result: dict, **params) -> None:
        """缓存分析结果"""
        if not result:
            logger.warning("尝试缓存空结果，已忽略")
            return
            
        key = self._generate_cache_key(text, **params)
        
        with self.lock:
            self.cache[key] = result
            logger.debug(f"已缓存结果: {key}")
    
    def clear_cache(self, text: str = None, **params) -> int:
        """清除缓存，可以指定文本和参数清除特定项，否则清除所有"""
        if text is None:
            # 清除所有缓存
            count = len(self.cache)
            with self.lock:
                self.cache.clear()
            logger.info(f"已清除所有缓存，共{count}项")
            return count
        
        # 清除特定项
        key = self._generate_cache_key(text, **params)
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                logger.info(f"已清除缓存项: {key}")
                return 1
        logger.info(f"未找到缓存项: {key}")
        return 0
    
    def get_stats(self) -> dict:
        """获取缓存统计信息"""
        return {
            "current_size": len(self.cache),
            "max_size": self.cache_size,
            "ttl": self.cache_ttl,
            "hits": logger.debug.__dict__.get('hits', 0),
            "misses": logger.debug.__dict__.get('misses', 0)
        }