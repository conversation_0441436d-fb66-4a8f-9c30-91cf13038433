"""
Result formatting utilities for the conversation analysis agent.
"""
import os
import json
from typing import Dict, Any, List
from dotenv import load_dotenv

load_dotenv()  # 加载环境变量
OUTPUT_FORMATS = os.getenv("OUTPUT_FORMATS", "md").split(",")


def format_individual_result(
    file_path: str,
    data_cleaning_result: Dict[str, Any],
    sentiment_result: Dict[str, Any],
    resistance_result: Dict[str, Any],
    competitive_result: Dict[str, Any],
    keyword_result: Dict[str, Any],
    token_usage: Dict[str, int],
    output_formats: list = None
) -> Dict[str, str]:
    """
    Format the individual result for a file based on specified output formats.

    Args:
        file_path: Path to the input file
        data_cleaning_result: Data cleaning result
        sentiment_result: Sentiment analysis result
        resistance_result: Resistance point analysis result
        competitive_result: Competitive product analysis result
        keyword_result: Keyword analysis result
        token_usage: Token usage information
        output_formats: List of output formats to generate

    Returns:
        Dictionary containing formatted results in specified formats
    """
    file_name = os.path.basename(file_path)
    result_data = {
        "file_name": file_name,
        "target_vehicle": data_cleaning_result.get("target_vehicle", "Unknown"),
        "target_vehicle_aspects": data_cleaning_result.get("target_vehicle_aspects", ""),
        "cleaned_text": data_cleaning_result.get("cleaned_text", ""),
        "sentiment_analysis": {
            "overall_sentiment": sentiment_result.get("overall_sentiment", "neutral"),
            "sentiment_details": sentiment_result.get("sentiment_details", []),
            "summary": sentiment_result.get("summary", "")
        },
        "resistance_points": {
            "points": resistance_result.get("points", []),
            "summary": resistance_result.get("summary", "")
        },
        "competitive_products": {
            "products": competitive_result.get("products", []),
            "summary": competitive_result.get("summary", "")
        },
        "keywords": {
            "keywords": keyword_result.get("keywords", []),
            "summary": keyword_result.get("summary", "")
        },
        "token_usage": token_usage
    }
    
    output_formats = output_formats or OUTPUT_FORMATS
    result_data['formatted'] = {}
    
    if "md" in output_formats:
        result_data['formatted']["md"] = generate_markdown_output(result_data)
    
    return result_data

def generate_markdown_output(result: Dict[str, Any]) -> str:
    """
    Format the result as Markdown.

    Args:
        result: Result dictionary

    Returns:
        Formatted Markdown result
    """

    text_result = []

    # File information
    text_result.append(f"# Analysis Result for {result['file_name']}")
    text_result.append("")

    # Target vehicle
    text_result.append(f"## Target Vehicle: {result['target_vehicle']}")
    if 'target_vehicle_aspects' in result and result['target_vehicle_aspects']:
        text_result.append(f"关注点: {result['target_vehicle_aspects']}")
    text_result.append("")

    # Sentiment analysis
    text_result.append("## Sentiment Analysis")
    text_result.append(f"Overall Sentiment: {result['sentiment_analysis']['overall_sentiment']}")
    text_result.append(f"Summary: {result['sentiment_analysis']['summary']}")
    text_result.append("Details:")
    for detail in result['sentiment_analysis']['sentiment_details']:
        text_result.append(f"- {detail['aspect']}: {detail['sentiment']} - {detail['explanation']}")
    text_result.append("")

    # Resistance points
    text_result.append("## Resistance Points")
    text_result.append(f"Summary: {result['resistance_points']['summary']}")
    text_result.append("Points:")
    for point in result['resistance_points']['points']:
        text_result.append(f"- {point['point']} ({point['category']}, {point['severity']})")

        # Check for explanation field with or without leading space
        if 'explanation' in point:
            text_result.append(f"  {point['explanation']}")
        elif ' explanation' in point:
            text_result.append(f"  {point[' explanation']}")
        else:
            # If no explanation field is found, skip it
            pass
    text_result.append("")

    # Competitive products
    text_result.append("## Competitive Products")
    text_result.append(f"Summary: {result['competitive_products']['summary']}")
    text_result.append("Products:")
    for product in result['competitive_products']['products']:
        if 'model' in product and 'count' in product:
            text_result.append(f"- {product['model']} (Mentions: {product['count']})")
        elif 'model' in product:
            text_result.append(f"- {product['model']}")
        else:
            # If model doesn't exist, skip this product
            continue
    text_result.append("")

    # Keywords
    text_result.append("## Keywords")
    text_result.append(f"Summary: {result['keywords']['summary']}")

    # Only process keywords if they exist
    if result['keywords']['keywords'] and len(result['keywords']['keywords']) > 0:
        text_result.append("Keywords:")
        for keyword in result['keywords']['keywords']:
            # Make sure keyword has the required fields
            if 'keyword' not in keyword or 'category' not in keyword:
                continue

            # Build the keyword line based on available fields
            keyword_line = f"- {keyword['keyword']} ({keyword['category']}"

            if 'importance' in keyword:
                keyword_line += f", Importance: {keyword['importance']}"

            if 'count' in keyword:
                keyword_line += f", Count: {keyword['count']}"

            keyword_line += ")"
            text_result.append(keyword_line)
    else:
        text_result.append("No keywords available.")
    text_result.append("")

    # Token usage
    text_result.append("## Token Usage")
    text_result.append(f"Total: {result['token_usage']['total']}")

    # Check if unified analysis was used
    if result['token_usage'].get('unified_analysis', 0) > 0:
        text_result.append(f"Unified Analysis: {result['token_usage']['unified_analysis']}")
    else:
        # Show individual analysis token usage
        text_result.append(f"Data Cleaning: {result['token_usage']['data_cleaning']}")
        text_result.append(f"Sentiment Analysis: {result['token_usage']['sentiment_analysis']}")
        text_result.append(f"Resistance Points: {result['token_usage']['resistance_points']}")
        text_result.append(f"Competitive Products: {result['token_usage']['competitive_products']}")
        text_result.append(f"Keywords: {result['token_usage']['keywords']}")

    return "\n".join(text_result)
