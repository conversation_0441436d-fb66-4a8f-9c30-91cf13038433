"""
File handling utilities for the conversation analysis agent.
"""
import os
import json
from typing import List, Dict, Any


def get_input_files(input_dir: str, limit: int = None) -> List[str]:
    """
    Get a list of text files from the input directory.

    Args:
        input_dir: Directory containing input files
        limit: Maximum number of files to return

    Returns:
        List of file paths
    """
    if not os.path.exists(input_dir):
        os.makedirs(input_dir)

    files = [
        os.path.join(input_dir, f) for f in os.listdir(input_dir)
        if f.endswith('.txt') and os.path.isfile(os.path.join(input_dir, f))
    ]

    if limit:
        return files[:limit]
    return files


def read_text_file(file_path: str) -> str:
    """
    Read text from a file.

    Args:
        file_path: Path to the file

    Returns:
        Text content of the file
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read()


def save_result(result: Dict[str, Any], output_path: str) -> None:
    """
    Save analysis result to a file.

    Args:
        result: Analysis result dictionary
        output_path: Path to save the result
    """
    with open(output_path, 'w', encoding='utf-8') as file:
        json.dump(result, file, ensure_ascii=False, indent=2)


def save_text_result(result: str, output_path: str) -> None:
    """
    Save text result to a file.

    Args:
        result: Text result
        output_path: Path to save the result
    """
    with open(output_path, 'w', encoding='utf-8') as file:
        file.write(result)


def aggregate_results(result_files: List[str], output_path: str) -> Dict[str, Any]:
    """
    Aggregate results from multiple files.

    Args:
        result_files: List of result file paths
        output_path: Path to save the aggregated result

    Returns:
        Aggregated result dictionary
    """
    aggregated_results = {
        "files_processed": len(result_files),
        "results": []
    }

    total_token_usage = {
        "data_cleaning": 0,
        "sentiment_analysis": 0,
        "resistance_points": 0,
        "competitive_products": 0,
        "keywords": 0,
        "total": 0
    }

    for file_path in result_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                result = json.load(file)

                # Add the result to the aggregated results
                aggregated_results["results"].append(result)

                # Aggregate token usage
                if "token_usage" in result:
                    for key in total_token_usage:
                        if key in result["token_usage"]:
                            total_token_usage[key] += result["token_usage"][key]
        except Exception as e:
            print(f"Error processing file {file_path}: {str(e)}")

    aggregated_results["token_usage"] = total_token_usage

    # Save the aggregated result
    with open(output_path, 'w', encoding='utf-8') as file:
        json.dump(aggregated_results, file, ensure_ascii=False, indent=2)

    return aggregated_results
