"""
Token usage tracking for the conversation analysis agent.
"""
from typing import Dict, Any, Optional


class TokenCounter:
    """
    Class for tracking token usage in API calls.
    """

    def __init__(self):
        """
        Initialize the token counter.
        """
        self.token_usage = {
            "data_cleaning": 0,
            "sentiment_analysis": 0,
            "resistance_points": 0,
            "competitive_products": 0,
            "keywords": 0,
            "unified_analysis": 0,
            "total": 0
        }

    def update_usage(self, category: str, input_tokens: int, output_tokens: int) -> None:
        """
        Update token usage for a specific category.

        Args:
            category: Category of the analysis
            input_tokens: Number of input tokens
            output_tokens: Number of output tokens
        """
        if category in self.token_usage:
            total_tokens = input_tokens + output_tokens
            self.token_usage[category] += total_tokens
            self.token_usage["total"] += total_tokens

    def get_usage(self) -> Dict[str, int]:
        """
        Get the current token usage.

        Returns:
            Dictionary of token usage by category
        """
        return self.token_usage

    def extract_usage_from_response(self, response: Any, category: str) -> None:
        """
        Extract token usage from a model response and update the counter.

        Args:
            response: Response from the model
            category: Category of the analysis
        """
        print(f"[TokenCounter] Extracting token usage for {category}")
        print(f"[TokenCounter] Response type: {type(response)}")

        # Debug response structure
        print(f"[TokenCounter] Response attributes: {dir(response)[:10]}...")

        try:
            # Extract token usage from response
            print(f"[TokenCounter] Attempting to access usage attribute")
            usage = response.usage
            print(f"[TokenCounter] Usage attributes: {dir(usage)[:10]}...")

            input_tokens = usage.input_tokens
            output_tokens = usage.output_tokens

            print(f"[TokenCounter] Token usage for {category}: {input_tokens} input, {output_tokens} output")
            self.update_usage(category, input_tokens, output_tokens)
            print(f"[TokenCounter] Updated token usage for {category}")
        except (AttributeError, KeyError) as e:
            print(f"[TokenCounter] Error extracting token usage: {str(e)}")
            print(f"[TokenCounter] Falling back to estimation method")

            # If token usage information is not available, estimate based on response length
            try:
                print(f"[TokenCounter] Attempting to access response content")

                # Handle both dashscope and OpenAI-compatible response formats
                if hasattr(response, 'output'):
                    if hasattr(response.output, 'choices') and len(response.output.choices) > 0:
                        # Dashscope format
                        content = response.output.choices[0].message.content
                    elif 'choices' in response.output and len(response.output['choices']) > 0:
                        # OpenAI-compatible format
                        content = response.output['choices'][0]['message']['content']
                    else:
                        raise AttributeError("Could not find content in response")
                else:
                    raise AttributeError("Response has no output attribute")

                print(f"[TokenCounter] Content length: {len(content)} characters")

                # Rough estimate: 1 token ≈ 3 characters for English, adjust for other languages
                estimated_output_tokens = len(content) // 3
                estimated_input_tokens = len(str(response.request)) // 3

                print(f"[TokenCounter] Estimated token usage for {category}: {estimated_input_tokens} input, {estimated_output_tokens} output")
                self.update_usage(category, estimated_input_tokens, estimated_output_tokens)
                print(f"[TokenCounter] Updated token usage with estimates for {category}")
            except (AttributeError, KeyError, IndexError) as e:
                print(f"[TokenCounter] Error estimating token usage: {str(e)}")
                print(f"[TokenCounter] Falling back to default values")

                # If we can't even estimate, use a default value
                default_input_tokens = 500
                default_output_tokens = 300
                print(f"[TokenCounter] Using default token usage for {category}: {default_input_tokens} input, {default_output_tokens} output")
                self.update_usage(category, default_input_tokens, default_output_tokens)
                print(f"[TokenCounter] Updated token usage with default values for {category}")

        # Print current token usage after update
        current_usage = self.get_usage()
        print(f"[TokenCounter] Current token usage: {current_usage}")
