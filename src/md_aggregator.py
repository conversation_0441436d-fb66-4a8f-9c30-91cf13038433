"""
Module for generating and updating All_result.md with aggregated analysis results.
"""
import os
import json
import re
from typing import Dict, Any, List
from collections import defaultdict, Counter


def get_target_vehicle_list() -> List[str]:
    """
    Extract the target vehicle list from the unified_analysis.md file.

    Returns:
        List of target vehicle models
    """
    try:
        with open("prompts/unified_analysis.md", 'r', encoding='utf-8') as file:
            content = file.read()

        # Find the line with the target vehicle list
        match = re.search(r"目标车型必须从以下列表中识别.*?：(.*?)，如果未提及则返回", content)
        if match:
            # Extract the list of vehicles
            vehicles_str = match.group(1)
            # Remove quotes and split by commas
            vehicles = [v.strip("'") for v in vehicles_str.split(",")]
            return vehicles
        else:
            # Fallback to hardcoded list if pattern not found
            return ["ID.3", "ID.3 GTX", "ID.4X", "ID.6X", "ID.ERA", "帕萨特Pro", "帕萨特",
                    "凌渡L", "朗逸", "朗逸新锐", "Polo Plus", "途昂Pro", "途昂", "途昂X",
                    "途观L Pro", "途观L", "途观X", "途岳", "途岳新锐", "威然"]
    except Exception as e:
        print(f"Error reading target vehicle list: {e}")
        # Fallback to hardcoded list
        return ["ID.3", "ID.3 GTX", "ID.4X", "ID.6X", "ID.ERA", "帕萨特Pro", "帕萨特",
                "凌渡L", "朗逸", "朗逸新锐", "Polo Plus", "途昂Pro", "途昂", "途昂X",
                "途观L Pro", "途观L", "途观X", "途岳", "途岳新锐", "威然"]

# Get the target vehicle list
TARGET_VEHICLES = get_target_vehicle_list()


def initialize_md_file(output_dir: str) -> None:
    """
    Initialize the All_result.md file with headers for each section.

    Args:
        output_dir: Directory to save the file
    """
    md_path = os.path.join(output_dir, "All_result.md")

    # Check if file already exists
    if os.path.exists(md_path):
        print(f"All_result.md already exists at {md_path}")
        return

    # Create initial content with headers
    content = [
        "# 汽车销售对话分析汇总",
        "",
        "## 1. 产品关注度",
        "| 产品 | 提及次数 |",
        "| --- | --- |",
        "",
        "## 2. 客户关注点",
        "| 关注点 | 提及次数 |",
        "| --- | --- |",
        "",
        "## 3. 销售顾问提及点",
        "| 提及点 | 提及次数 |",
        "| --- | --- |",
        "",
        "## 4. 固定词汇提及次数",
        "| 词汇 | 提及次数 |",
        "| --- | --- |",
        "",
        "## 6. 产品情感及关注点",
        "### 情感统计",
        "| 产品 | Positive | Neutral | Negative |",
        "| --- | --- | --- | --- |",
        "",
        "### 关注点统计",
        "| 产品 | 关注点 | 提及次数 |",
        "| --- | --- | --- |",
        "",
        "## 7. 本品竞品统计",
        "| 本品 | 竞品 | 提及次数 |",
        "| --- | --- | --- |",
        "",
        "## 8. 客户抗拒点统计",
        "| 抗拒点 | 提及次数 |",
        "| --- | --- |",
        "",
        "## 处理状态",
        "| 文件名 | 处理状态 |",
        "| --- | --- |",
        ""
    ]

    # Write to file
    with open(md_path, 'w', encoding='utf-8') as file:
        file.write('\n'.join(content))

    print(f"Initialized All_result.md at {md_path}")


def read_current_md(output_dir: str) -> Dict[str, Any]:
    """
    Read the current All_result.md file and parse its content into a structured format.

    Args:
        output_dir: Directory where the file is located

    Returns:
        Dictionary with parsed content
    """
    md_path = os.path.join(output_dir, "All_result.md")

    if not os.path.exists(md_path):
        initialize_md_file(output_dir)

    with open(md_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # Parse the content into a structured format
    data = {
        "product_attention": defaultdict(int),
        "customer_focus": defaultdict(int),
        "salesman_mentions": defaultdict(int),
        "fixed_word_mentions": defaultdict(int),
        "product_sentiment": defaultdict(lambda: {"positive": 0, "neutral": 0, "negative": 0}),
        "product_focus_points": defaultdict(lambda: defaultdict(int)),
        "competitive_products": defaultdict(lambda: defaultdict(int)),
        "resistance_points": defaultdict(int),
        "processed_files": {}
    }

    # Parse product attention
    lines = content.split('\n')
    section = None
    subsection = None

    for i, line in enumerate(lines):
        if line.startswith("## 1. 产品关注度"):
            section = "product_attention"
        elif line.startswith("## 2. 客户关注点"):
            section = "customer_focus"
        elif line.startswith("## 3. 销售顾问提及点"):
            section = "salesman_mentions"
        elif line.startswith("## 4. 固定词汇提及次数"):
            section = "fixed_word_mentions"
        elif line.startswith("## 6. 产品情感及关注点"):
            section = "product_sentiment"
        elif line.startswith("### 情感统计") and section == "product_sentiment":
            subsection = "sentiment"
        elif line.startswith("### 关注点统计") and section == "product_sentiment":
            subsection = "focus_points"
        elif line.startswith("## 7. 本品竞品统计"):
            section = "competitive_products"
            subsection = None
        elif line.startswith("## 8. 客户抗拒点统计"):
            section = "resistance_points"
            subsection = None
        elif line.startswith("## 处理状态"):
            section = "processed_files"
            subsection = None
        elif line.startswith("| ") and not line.startswith("| ---") and section:
            # Parse table rows
            cells = [cell.strip() for cell in line.split('|')[1:-1]]

            # Skip header rows
            if cells[0] == "产品" or cells[0] == "关注点" or cells[0] == "提及点" or cells[0] == "词汇" or \
               cells[0] == "产品" or cells[0] == "本品" or cells[0] == "抗拒点" or cells[0] == "文件名":
                continue

            try:
                if section == "product_attention" and len(cells) == 2:
                    data["product_attention"][cells[0]] = int(cells[1])
                elif section == "customer_focus" and len(cells) == 2:
                    data["customer_focus"][cells[0]] = int(cells[1])
                elif section == "salesman_mentions" and len(cells) == 2:
                    data["salesman_mentions"][cells[0]] = int(cells[1])
                elif section == "fixed_word_mentions" and len(cells) == 2:
                    data["fixed_word_mentions"][cells[0]] = int(cells[1])
                elif section == "product_sentiment" and subsection == "sentiment" and len(cells) == 4:
                    if cells[0] != "产品":  # Skip header row
                        data["product_sentiment"][cells[0]]["positive"] = int(cells[1])
                        data["product_sentiment"][cells[0]]["neutral"] = int(cells[2])
                        data["product_sentiment"][cells[0]]["negative"] = int(cells[3])
                elif section == "product_sentiment" and subsection == "focus_points" and len(cells) == 3:
                    data["product_focus_points"][cells[0]][cells[1]] = int(cells[2])
                elif section == "competitive_products" and len(cells) == 3:
                    data["competitive_products"][cells[0]][cells[1]] = int(cells[2])
                elif section == "resistance_points" and len(cells) == 2:
                    data["resistance_points"][cells[0]] = int(cells[1])
                elif section == "processed_files" and len(cells) == 2:
                    data["processed_files"][cells[0]] = cells[1]
            except ValueError:
                # Skip rows that can't be parsed (like headers)
                continue

    return data


def update_md_file(result: Dict[str, Any], output_dir: str) -> None:
    """
    Update All_result.md with results from a newly processed file.

    Args:
        result: Analysis result for a single file
        output_dir: Directory where the file is located
    """
    # Read current content
    current_data = read_current_md(output_dir)

    # Update each section
    update_product_attention(result, current_data)
    update_customer_focus(result, current_data)
    update_salesman_mentions(result, current_data)
    update_fixed_word_mentions(result, current_data)
    update_product_sentiment(result, current_data)
    update_competitive_products(result, current_data)
    update_resistance_points(result, current_data)

    # Update processed files
    current_data["processed_files"][result["file_name"]] = "已处理"

    # Format and write back to file
    md_content = format_md_content(current_data)
    md_path = os.path.join(output_dir, "All_result.md")

    with open(md_path, 'w', encoding='utf-8') as file:
        file.write(md_content)

    print(f"Updated All_result.md with results from {result['file_name']}")


def update_product_attention(result: Dict[str, Any], current_data: Dict[str, Any]) -> None:
    """
    Update the product attention section based on target_vehicle.

    Args:
        result: Analysis result for a single file
        current_data: Current data from All_result.md
    """
    target_vehicle = result.get("target_vehicle")

    # If target vehicle is unknown, try to find it in the competitive products
    if not target_vehicle or target_vehicle in ["Unknown", "unidentified"]:
        if "competitive_products" in result and "products" in result["competitive_products"]:
            for product in result["competitive_products"]["products"]:
                # Check if the product is a Volkswagen model that could be the target
                model = product.get("model", "")
                if model.startswith("途") or model in ["ID.3", "ID.3 GTX", "ID.4X", "ID.6X", "ID.ERA", "帕萨特Pro", "帕萨特", "凌渡L", "朗逸", "朗逸新锐", "Polo Plus", "途昂Pro", "途昂", "途昂X", "途观L Pro", "途观L", "途观X", "途岳", "途岳新锐", "威然"]:
                    target_vehicle = model
                    break

    # If we found a valid target vehicle, update the count
    if target_vehicle and target_vehicle not in ["Unknown", "unidentified"]:
        current_data["product_attention"][target_vehicle] += 1


def update_customer_focus(result: Dict[str, Any], current_data: Dict[str, Any]) -> None:
    """
    Update the customer focus section based on keywords.

    Args:
        result: Analysis result for a single file
        current_data: Current data from All_result.md
    """
    # 定义客户关注点的分类
    focus_categories = {
        "价格": ["价格", "费用", "多少钱", "优惠", "便宜", "贵", "折扣"],
        "配置": ["配置", "功能", "设备", "装备", "选装", "标配"],
        "性能": ["性能", "动力", "加速", "马力", "扭矩", "续航", "电池", "油耗"],
        "外观": ["外观", "造型", "设计", "颜色", "漆面", "轮毂"],
        "内饰": ["内饰", "座椅", "方向盘", "中控", "屏幕", "空间", "储物"],
        "安全": ["安全", "碰撞", "气囊", "辅助", "驾驶", "制动"],
        "舒适": ["舒适", "噪音", "隔音", "减震", "悬挂", "空调"],
        "质保": ["质保", "保修", "售后", "服务"],
        "其他": []  # 默认分类
    }

    if "keywords" in result and "keywords" in result["keywords"]:
        for keyword in result["keywords"]["keywords"]:
            if keyword.get("category") == "customer":
                keyword_text = keyword.get("keyword", "")
                # 转换count为整数
                count_value = keyword.get("count", 1)
                count = int(count_value) if isinstance(count_value, str) else count_value

                # 根据关键词分类
                category_found = False
                for category, keywords in focus_categories.items():
                    if any(kw in keyword_text for kw in keywords):
                        # 使用分类名称作为关注点
                        current_data["customer_focus"][category] += count
                        category_found = True
                        break

                # 如果没有找到匹配的分类，使用"其他"分类
                if not category_found:
                    current_data["customer_focus"]["其他"] += count

    # 如果有target_vehicle_aspects，也添加到客户关注点
    if "target_vehicle_aspects" in result and result["target_vehicle_aspects"]:
        aspects = result["target_vehicle_aspects"]
        # 根据关键词分类
        for category, keywords in focus_categories.items():
            if any(kw in aspects for kw in keywords):
                # 使用分类名称作为关注点
                current_data["customer_focus"][category] += 1


def update_salesman_mentions(result: Dict[str, Any], current_data: Dict[str, Any]) -> None:
    """
    Update the salesman mentions section based on keywords.

    Args:
        result: Analysis result for a single file
        current_data: Current data from All_result.md
    """
    # 定义销售顾问提及点的分类
    mention_categories = {
        "产品优势": ["优势", "特点", "亮点", "独特", "领先", "先进"],
        "价格政策": ["价格", "优惠", "折扣", "促销", "补贴", "政策"],
        "配置介绍": ["配置", "功能", "装备", "设备", "标配", "选装"],
        "性能参数": ["性能", "参数", "动力", "续航", "油耗", "电池"],
        "售后服务": ["服务", "售后", "保修", "质保", "维修", "保养"],
        "试驾体验": ["试驾", "体验", "感受", "驾驶", "操控"],
        "交付周期": ["交付", "周期", "等待", "现车", "库存"],
        "其他": []  # 默认分类
    }

    if "keywords" in result and "keywords" in result["keywords"]:
        for keyword in result["keywords"]["keywords"]:
            if keyword.get("category") == "salesman":
                keyword_text = keyword.get("keyword", "")
                # 转换count为整数
                count_value = keyword.get("count", 1)
                count = int(count_value) if isinstance(count_value, str) else count_value

                # 根据关键词分类
                category_found = False
                for category, keywords in mention_categories.items():
                    if any(kw in keyword_text for kw in keywords):
                        # 使用分类名称作为提及点
                        current_data["salesman_mentions"][category] += count
                        category_found = True
                        break

                # 如果没有找到匹配的分类，使用"其他"分类
                if not category_found:
                    current_data["salesman_mentions"]["其他"] += count


def update_fixed_word_mentions(result: Dict[str, Any], current_data: Dict[str, Any]) -> None:
    """
    Update the fixed word mentions section based on keywords.

    Args:
        result: Analysis result for a single file
        current_data: Current data from All_result.md
    """
    # Define common fixed words to track
    fixed_words = [
        "第五代EA888", "发动机", "底盘", "整车终身质保", "电池包",
        "活塞", "白车身", "空滤加注", "气味", "耐久板材",
        "假人", "改装", "松树机器人", "游戏手柄", "奶茶",
        "香肠", "美美与共套娃"
    ]

    # Initialize fixed words if they don't exist in current data
    for word in fixed_words:
        if word not in current_data["fixed_word_mentions"]:
            current_data["fixed_word_mentions"][word] = 0

    # Update counts from keywords
    if "keywords" in result and "keywords" in result["keywords"]:
        for keyword in result["keywords"]["keywords"]:
            if keyword.get("category") == "fixed word":
                keyword_text = keyword.get("keyword", "")
                # Convert count to integer if it's a string
                count_value = keyword.get("count", 0)
                count = int(count_value) if isinstance(count_value, str) else count_value
                if keyword_text in fixed_words:
                    current_data["fixed_word_mentions"][keyword_text] += count


def update_product_sentiment(result: Dict[str, Any], current_data: Dict[str, Any]) -> None:
    """
    Update the product sentiment section based on sentiment analysis.

    Args:
        result: Analysis result for a single file
        current_data: Current data from All_result.md
    """
    target_vehicle = result.get("target_vehicle")
    target_vehicle_aspects = result.get("target_vehicle_aspects", "")

    # If target vehicle is unknown, try to find it in the competitive products
    if not target_vehicle or target_vehicle in ["Unknown", "unidentified"]:
        if "competitive_products" in result and "products" in result["competitive_products"]:
            for product in result["competitive_products"]["products"]:
                # Check if the product is a Volkswagen model that could be the target
                model = product.get("model", "")
                if model.startswith("途") or model in ["ID.3", "ID.3 GTX", "ID.4X", "ID.6X", "ID.ERA", "帕萨特Pro", "帕萨特", "凌渡L", "朗逸", "朗逸新锐", "Polo Plus", "途昂Pro", "途昂", "途昂X", "途观L Pro", "途观L", "途观X", "途岳", "途岳新锐", "威然"]:
                    target_vehicle = model
                    break

    if not target_vehicle or target_vehicle in ["Unknown", "unidentified"]:
        # If still unknown, use a default value from the conversation context
        # Check keywords for vehicle mentions
        if "keywords" in result and "keywords" in result["keywords"]:
            for keyword in result["keywords"]["keywords"]:
                if keyword.get("keyword") in ["ID.3", "ID.3 GTX", "ID.4X", "ID.6X", "ID.ERA", "帕萨特Pro", "帕萨特", "凌渡L", "朗逸", "朗逸新锐", "Polo Plus", "途昂Pro", "途昂", "途昂X", "途观L Pro", "途观L", "途观X", "途岳", "途岳新锐", "威然"]:
                    target_vehicle = keyword.get("keyword")
                    break

    if not target_vehicle or target_vehicle in ["Unknown", "unidentified"]:
        return

    # Update sentiment counts
    sentiment = result.get("sentiment_analysis", {}).get("overall_sentiment", "neutral").lower()

    # Initialize sentiment counts if they don't exist
    if target_vehicle not in current_data["product_sentiment"]:
        current_data["product_sentiment"][target_vehicle] = {
            "positive": 0,
            "neutral": 0,
            "negative": 0
        }

    # Update the sentiment count
    current_data["product_sentiment"][target_vehicle][sentiment] += 1

    # 定义产品关注点的分类
    focus_categories = {
        "价格": ["价格", "费用", "多少钱", "优惠", "便宜", "贵", "折扣"],
        "配置": ["配置", "功能", "设备", "装备", "选装", "标配"],
        "性能": ["性能", "动力", "加速", "马力", "扭矩", "续航", "电池", "油耗"],
        "外观": ["外观", "造型", "设计", "颜色", "漆面", "轮毂"],
        "内饰": ["内饰", "座椅", "方向盘", "中控", "屏幕", "空间", "储物"],
        "安全": ["安全", "碰撞", "气囊", "辅助", "驾驶", "制动"],
        "舒适": ["舒适", "噪音", "隔音", "减震", "悬挂", "空调"],
        "质保": ["质保", "保修", "售后", "服务"],
        "其他": []  # 默认分类
    }

    # 首先从target_vehicle_aspects中提取关注点
    if target_vehicle_aspects:
        # 初始化产品关注点
        if target_vehicle not in current_data["product_focus_points"]:
            current_data["product_focus_points"][target_vehicle] = defaultdict(int)

        # 根据关键词分类
        for category, keywords in focus_categories.items():
            if any(kw in target_vehicle_aspects for kw in keywords):
                current_data["product_focus_points"][target_vehicle][category] += 1

    # 从情感分析中提取关注点
    if "sentiment_analysis" in result and "sentiment_details" in result["sentiment_analysis"]:
        # 初始化产品关注点
        if target_vehicle not in current_data["product_focus_points"]:
            current_data["product_focus_points"][target_vehicle] = defaultdict(int)

        for detail in result["sentiment_analysis"]["sentiment_details"]:
            aspect = detail.get("aspect", "")
            if aspect:
                # 根据关键词分类
                category_found = False
                for category, keywords in focus_categories.items():
                    if any(kw in aspect for kw in keywords):
                        current_data["product_focus_points"][target_vehicle][category] += 1
                        category_found = True
                        break

                # 如果没有找到匹配的分类，使用"其他"分类
                if not category_found:
                    current_data["product_focus_points"][target_vehicle]["其他"] += 1


def update_competitive_products(result: Dict[str, Any], current_data: Dict[str, Any]) -> None:
    """
    Update the competitive products section.

    Args:
        result: Analysis result for a single file
        current_data: Current data from All_result.md
    """
    target_vehicle = result.get("target_vehicle")
    if not target_vehicle or target_vehicle in ["Unknown", "unidentified"]:
        # If target vehicle is unknown, try to find it in the competitive products
        if "competitive_products" in result and "products" in result["competitive_products"]:
            for product in result["competitive_products"]["products"]:
                # Check if the product is a Volkswagen model that could be the target
                model = product.get("model", "")
                if model.startswith("途") or model in ["ID.3", "ID.3 GTX", "ID.4X", "ID.6X", "ID.ERA", "帕萨特Pro", "帕萨特", "凌渡L", "朗逸", "朗逸新锐", "Polo Plus", "途昂Pro", "途昂", "途昂X", "途观L Pro", "途观L", "途观X", "途岳", "途岳新锐", "威然"]:
                    target_vehicle = model
                    break

    if not target_vehicle or target_vehicle in ["Unknown", "unidentified"]:
        return

    if "competitive_products" in result and "products" in result["competitive_products"]:
        for product in result["competitive_products"]["products"]:
            # Skip if the product is the same as the target vehicle
            if product.get("model") == target_vehicle:
                continue

            comp_product = product.get("model", "")
            if not comp_product:
                continue

            # Convert count to integer if it's a string
            count_value = product.get("count", 1)
            count = int(count_value) if isinstance(count_value, str) else count_value

            # Initialize if not exists
            if target_vehicle not in current_data["competitive_products"]:
                current_data["competitive_products"][target_vehicle] = defaultdict(int)

            current_data["competitive_products"][target_vehicle][comp_product] += count


def update_resistance_points(result: Dict[str, Any], current_data: Dict[str, Any]) -> None:
    """
    Update the resistance points section.

    Args:
        result: Analysis result for a single file
        current_data: Current data from All_result.md
    """
    # 定义抗拒点的分类
    resistance_categories = {
        "价格抗拒": ["价格", "贵", "便宜", "优惠", "折扣", "成本", "预算"],
        "性能担忧": ["性能", "动力", "加速", "续航", "油耗", "电池", "里程"],
        "配置不满": ["配置", "功能", "装备", "设备", "标配", "选装"],
        "质量疑虑": ["质量", "可靠", "耐用", "故障", "问题", "维修"],
        "服务顾虑": ["服务", "售后", "保修", "保养", "维护"],
        "交付周期": ["交付", "等待", "周期", "现车", "库存"],
        "竞品对比": ["竞品", "比较", "对比", "其他品牌", "其他车型"],
        "其他抗拒": []  # 默认分类
    }

    if "resistance_points" in result and "points" in result["resistance_points"]:
        for point in result["resistance_points"]["points"]:
            point_text = point.get("point", "")
            if not point_text:
                continue

            # 根据抗拒点内容和类别进行分类
            category = point.get("category", "").lower()

            # 根据类别和内容确定抗拒点分类
            resistance_category = "其他抗拒"  # 默认分类

            # 首先尝试根据类别匹配
            if "price" in category:
                resistance_category = "价格抗拒"
            elif any(word in category for word in ["performance", "power", "battery"]):
                resistance_category = "性能担忧"
            elif any(word in category for word in ["feature", "configuration", "equipment"]):
                resistance_category = "配置不满"
            elif any(word in category for word in ["quality", "reliability", "durability"]):
                resistance_category = "质量疑虑"
            elif any(word in category for word in ["service", "warranty", "maintenance"]):
                resistance_category = "服务顾虑"
            elif any(word in category for word in ["delivery", "wait", "inventory"]):
                resistance_category = "交付周期"
            elif any(word in category for word in ["competitor", "comparison"]):
                resistance_category = "竞品对比"
            else:
                # 如果类别没有匹配，尝试根据内容匹配
                for cat, keywords in resistance_categories.items():
                    if any(kw in point_text.lower() for kw in keywords):
                        resistance_category = cat
                        break

            # 更新抗拒点计数
            current_data["resistance_points"][resistance_category] += 1


def mark_file_as_failed(file_name: str, error_message: str, output_dir: str) -> None:
    """
    Mark a file as failed in All_result.md.

    Args:
        file_name: Name of the file that failed
        error_message: Error message to include
        output_dir: Directory where All_result.md is located
    """
    # Read current content
    current_data = read_current_md(output_dir)

    # Update processed files with failure status
    current_data["processed_files"][file_name] = f"处理失败 - {error_message}"

    # Format and write back to file
    md_content = format_md_content(current_data)
    md_path = os.path.join(output_dir, "All_result.md")

    with open(md_path, 'w', encoding='utf-8') as file:
        file.write(md_content)

    print(f"Marked file {file_name} as failed in All_result.md")


def get_failed_files(output_dir: str) -> List[str]:
    """
    Get a list of files that failed processing from All_result.md.

    Args:
        output_dir: Directory where All_result.md is located

    Returns:
        List of file names that failed processing
    """
    # Read current content
    current_data = read_current_md(output_dir)

    # Find files with failure status
    failed_files = []
    for file_name, status in current_data["processed_files"].items():
        if status.startswith("处理失败"):
            failed_files.append(file_name)

    return failed_files


def format_md_content(data: Dict[str, Any]) -> str:
    """
    Format the data into markdown content.

    Args:
        data: Structured data to format

    Returns:
        Formatted markdown content
    """
    content = [
        "# 汽车销售对话分析汇总",
        ""
    ]

    # 1. Product Attention
    content.extend([
        "## 1. 产品关注度",
        "| 产品 | 提及次数 |",
        "| --- | --- |"
    ])

    for product, count in sorted(data["product_attention"].items(), key=lambda x: x[1], reverse=True):
        content.append(f"| {product} | {count} |")
    content.append("")

    # 2. Customer Focus
    content.extend([
        "## 2. 客户关注点",
        "| 关注点 | 提及次数 |",
        "| --- | --- |"
    ])

    for focus, count in sorted(data["customer_focus"].items(), key=lambda x: x[1], reverse=True):
        content.append(f"| {focus} | {count} |")
    content.append("")

    # 3. Salesman Mentions
    content.extend([
        "## 3. 销售顾问提及点",
        "| 提及点 | 提及次数 |",
        "| --- | --- |"
    ])

    for mention, count in sorted(data["salesman_mentions"].items(), key=lambda x: x[1], reverse=True):
        content.append(f"| {mention} | {count} |")
    content.append("")

    # 4. Fixed Word Mentions
    content.extend([
        "## 4. 固定词汇提及次数",
        "| 词汇 | 提及次数 |",
        "| --- | --- |"
    ])

    for word, count in sorted(data["fixed_word_mentions"].items(), key=lambda x: x[1], reverse=True):
        content.append(f"| {word} | {count} |")
    content.append("")

    # 6. Product Sentiment and Focus Points
    content.extend([
        "## 6. 产品情感及关注点",
        "### 情感统计",
        "| 产品 | Positive | Neutral | Negative |",
        "| --- | --- | --- | --- |"
    ])

    for product, sentiments in sorted(data["product_sentiment"].items()):
        content.append(f"| {product} | {sentiments['positive']} | {sentiments['neutral']} | {sentiments['negative']} |")
    content.append("")

    content.extend([
        "### 关注点统计",
        "| 产品 | 关注点 | 提及次数 |",
        "| --- | --- | --- |"
    ])

    for product, focus_points in sorted(data["product_focus_points"].items()):
        for focus, count in sorted(focus_points.items(), key=lambda x: x[1], reverse=True):
            content.append(f"| {product} | {focus} | {count} |")
    content.append("")

    # 7. Competitive Products
    content.extend([
        "## 7. 本品竞品统计",
        "| 本品 | 竞品 | 提及次数 |",
        "| --- | --- | --- |"
    ])

    for product, competitors in sorted(data["competitive_products"].items()):
        for competitor, count in sorted(competitors.items(), key=lambda x: x[1], reverse=True):
            content.append(f"| {product} | {competitor} | {count} |")
    content.append("")

    # 8. Resistance Points
    content.extend([
        "## 8. 客户抗拒点统计",
        "| 抗拒点 | 提及次数 |",
        "| --- | --- |"
    ])

    for point, count in sorted(data["resistance_points"].items(), key=lambda x: x[1], reverse=True):
        content.append(f"| {point} | {count} |")
    content.append("")

    # Processing Status
    content.extend([
        "## 处理状态",
        "| 文件名 | 处理状态 |",
        "| --- | --- |"
    ])

    for file_name, status in sorted(data["processed_files"].items()):
        content.append(f"| {file_name} | {status} |")

    return '\n'.join(content)
