"""
Unified analyzer for the conversation analysis agent.
This analyzer combines all analysis tasks into a single LLM call.
"""
import json
import os
from typing import Dict, Any, Optional
from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field

from src.analyzers.base_analyzer import BaseAnalyzer
from src.models.analysis_models import UnifiedAnalysisOutput
from src.token_counter import TokenCounter
from src.cache import AnalysisCache



class UnifiedAnalyzer(BaseAnalyzer):
    """
    Unified analyzer that performs all analysis tasks in a single LLM call.
    """

    def __init__(
        self,
        model_name: str = "qwen-turbo-latest",
        prompt_template_path: str = "prompts/unified_analysis.md",
        review_prompt_template_path: str = "prompts/review_analysis.md",
        token_counter: Optional[TokenCounter] = None
    ):
        """
        Initialize the unified analyzer.

        Args:
            model_name: Name of the model to use
            prompt_template_path: Path to the prompt template file
            token_counter: Token counter instance
        """
        super().__init__(model_name, prompt_template_path, token_counter)
        self.review_prompt_template = self._load_review_prompt_template(review_prompt_template_path)

        # Set up the output parser
        self.parser = JsonOutputParser(pydantic_object=UnifiedAnalysisOutput)
        
        # Initialize cache
        self.cache = AnalysisCache()

    def _load_review_prompt_template(self, path: str) -> str:
        """Load the review prompt template from a file."""
        if not os.path.exists(path):
            raise ValueError(f"Review prompt template file not found: {path}")
        
        with open(path, 'r', encoding='utf-8') as file:
            return file.read()

    def _build_review_prompt(self, initial_result: Dict[str, Any], text: str) -> str:
        """构建审查提示词"""
        return self.review_prompt_template.replace("{text}", text).replace("{initial_result}", json.dumps(initial_result))

    @BaseAnalyzer.handle_analysis_errors
    async def analyze(self, text: str) -> Dict[str, Any]:
        """
        Perform unified analysis on the text.

        Args:
            text: Text to analyze

        Returns:
            Unified analysis result
        """
        # Check cache first
        cached_result = self.cache.get_cached_result(text)
        if cached_result:
            return cached_result
        
        # Format the prompt with the text
        prompt = self.prompt_template.replace("{text}", text)

        # Call the model
        response = await self.call_model(prompt)

        # Update token usage
        self.token_counter.extract_usage_from_response(response, "unified_analysis")

        # Parse the response using base class method
        result = self._parse_response(response)
        
        # Cache the result
        self.cache.cache_result(text, result)
        
        return result

    def _parse_response(self, response):
        """Parse the LLM response using the configured parser."""
        if hasattr(response, 'output') and 'choices' in response.output and len(response.output['choices']) > 0:
            content = response.output['choices'][0]['message']['content']
            cleaned_content = self.clean_json_content(content)
            return self.parser.parse(cleaned_content)
        else:
            raise ValueError("Invalid response structure from LLM. No choices found.")
