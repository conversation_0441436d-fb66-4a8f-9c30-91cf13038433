"""
Data cleaning analyzer for the conversation analysis agent.
"""
import json
from typing import Dict, Any, Optional
from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field

from src.analyzers.base_analyzer import BaseAnalyzer
from src.token_counter import TokenCounter


class DataCleaningOutput(BaseModel):
    """Output schema for data cleaning."""
    target_vehicle: str = Field(description="The specific vehicle model the customer is primarily interested in")
    cleaned_text: str = Field(description="The cleaned conversation text")


class DataCleaner(BaseAnalyzer):
    """
    Analyzer for cleaning data and identifying target vehicle.
    """

    def __init__(
        self,
        model_name: str = "qwen-turbo-latest",
        prompt_template_path: str = "prompts/data_cleaning.md",
        token_counter: Optional[TokenCounter] = None
    ):
        """
        Initialize the data cleaner.

        Args:
            model_name: Name of the model to use
            prompt_template_path: Path to the prompt template file
            token_counter: Token counter instance
        """
        super().__init__(model_name, prompt_template_path, token_counter)

        # Set up the output parser
        self.parser = JsonOutputParser(pydantic_object=DataCleaningOutput)

    async def analyze(self, text: str) -> Dict[str, Any]:
        """
        Clean the text and identify the target vehicle.

        Args:
            text: Text to analyze

        Returns:
            Cleaned text and target vehicle
        """
        # Format the prompt with the text
        prompt = self.prompt_template.replace("{text}", text)

        # Call the model
        response = await self.call_model(prompt)

        # Update token usage
        self.token_counter.extract_usage_from_response(response, "data_cleaning")

        # Parse the response
        try:
            # Handle both attribute and dictionary access for compatibility
            if hasattr(response.output, 'choices'):
                content = response.output.choices[0].message.content
            else:
                # Access as dictionary
                content = response.output['choices'][0]['message']['content']

            print(f"[DataCleaner] Response content length: {len(content)} characters")
            print(f"[DataCleaner] Response content preview: {content[:100]}...")

            # Check if the content contains an error message
            if content.startswith("Error:") or "Error: LLM response" in content:
                print(f"[DataCleaner] Detected error in response: {content}")
                return {
                    "target_vehicle": "Unknown",
                    "cleaned_text": text,
                    "error": content
                }

            # Clean the content using the helper method
            content = self.clean_json_content(content)

            result = json.loads(content)
            print(f"[DataCleaner] Successfully parsed JSON response")
            return result
        except Exception as e:
            print(f"[DataCleaner] Error parsing response: {str(e)}")

            # Check if the response contains an error message
            error_message = str(e)
            if hasattr(response, 'output') and 'choices' in response.output and len(response.output['choices']) > 0:
                content = response.output['choices'][0]['message']['content']
                if content.startswith("Error:"):
                    error_message = content

            # Fallback to a basic result if parsing fails
            target_vehicle = "Unknown"
            # Simple heuristic as fallback
            if "宝马" in text or "BMW" in text:
                if "X3" in text:
                    target_vehicle = "BMW X3"
                elif "X5" in text:
                    target_vehicle = "BMW X5"
            elif "奔驰" in text or "Mercedes" in text:
                if "GLE" in text:
                    target_vehicle = "Mercedes-Benz GLE"
            elif "奥迪" in text or "Audi" in text:
                if "Q7" in text:
                    target_vehicle = "Audi Q7"
            elif "Model X" in text:
                target_vehicle = "Model X"
            elif "途观" in text:
                target_vehicle = "途观"
            elif "途昂" in text:
                target_vehicle = "途昂"
            elif "帕萨特" in text:
                target_vehicle = "帕萨特"

            return {
                "target_vehicle": target_vehicle,
                "cleaned_text": text,
                "error": error_message
            }
