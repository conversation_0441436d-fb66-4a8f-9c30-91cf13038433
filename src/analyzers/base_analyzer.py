"""
Base analyzer class for the conversation analysis agent.
"""
import os
import json
import asyncio
import functools
from typing import Dict, Any, Optional

from src.token_counter import Token<PERSON>ounter
from src.llm_client import LocalLLMClient


class BaseAnalyzer:
    """
    Base class for all analyzers.
    """

    def __init__(
        self,
        model_name: str = None,
        prompt_template_path: str = None,
        token_counter: Optional[TokenCounter] = None,
        provider: str = None,
        rate_limit: int = None,
        time_window: int = None
    ):
        """
        Initialize the base analyzer.

        Args:
            model_name: Name of the model to use (if None, uses the one from .env)
            prompt_template_path: Path to the prompt template file
            token_counter: Token counter instance
            provider: Provider to use ("qwen" or "local", if None, uses the one from .env)
            rate_limit: API rate limit (requests per minute)
            time_window: API rate limit time window in seconds
        """
        self.model_name = model_name
        self.prompt_template_path = prompt_template_path
        self.token_counter = token_counter or TokenCounter()
        self.provider = provider or os.environ.get("MODEL_PROVIDER", "qwen")

        # Get rate limit configuration from environment if not provided
        self.rate_limit = rate_limit or int(os.environ.get("API_RATE_LIMIT", 800))
        self.time_window = time_window or int(os.environ.get("API_TIME_WINDOW", 60))

        # Initialize the LLM client with rate limiting
        # Don't pass model name to let the client use the appropriate model for the provider
        self.llm_client = LocalLLMClient(
            provider=self.provider,
            rate_limit=self.rate_limit,
            time_window=self.time_window
        )

        # Store the actual model name being used for logging
        self.model_name = self.llm_client.model

        # Load the prompt template
        self.prompt_template = self._load_prompt_template()

    def _load_prompt_template(self) -> str:
        """
        Load the prompt template from a file.

        Returns:
            Prompt template string
        """
        if not self.prompt_template_path or not os.path.exists(self.prompt_template_path):
            raise ValueError(f"Prompt template file not found: {self.prompt_template_path}")

        with open(self.prompt_template_path, 'r', encoding='utf-8') as file:
            template = file.read()

        return template

    @staticmethod
    def handle_analysis_errors(func):
        """Decorator for handling analysis errors uniformly."""
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            try:
                result = await func(self, *args, **kwargs)
                self._log_analysis_result(result, success=True)
                return result
            except Exception as e:
                error_msg = f"Analysis failed: {str(e)}"
                self._log_analysis_result(error_msg, success=False)
                return self._create_error_result(error_msg)
        return wrapper

    @handle_analysis_errors
    async def analyze(self, text: str) -> Dict[str, Any]:
        """
        Analyze the text using the model.

        Args:
            text: Text to analyze

        Returns:
            Analysis result
        """
        raise NotImplementedError("Subclasses must implement this method")

    def _create_error_result(self, error_msg: str) -> Dict[str, Any]:
        """Create a standardized error result."""
        return {
            "error": error_msg,
            "data_cleaning": {
                "target_vehicle": "unidentified",
                "target_vehicle_aspects": "",
                "cleaned_text": ""
            },
            "sentiment_analysis": {
                "overall_sentiment": "neutral",
                "sentiment_details": [],
                "summary": error_msg
            },
            "resistance_points": {
                "points": [],
                "summary": error_msg
            },
            "competitive_products": {
                "products": [],
                "summary": error_msg
            },
            "keywords": {
                "keywords": [],
                "summary": error_msg
            }
        }

    def _get_result_summary(self, result: Dict[str, Any]) -> str:
        """Generate a brief summary of the analysis result."""
        if "error" in result:
            return f"Error: {result['error']}"
            
        sentiment = result.get("sentiment_analysis", {}).get("overall_sentiment", "unknown")
        keywords_count = len(result.get("keywords", {}).get("keywords", []))
        resistance_points = len(result.get("resistance_points", {}).get("points", []))
        
        return f"Sentiment: {sentiment}, Keywords: {keywords_count}, Resistance Points: {resistance_points}"

    def _log_analysis_result(self, result: Any, success: bool):
        """Log the analysis result in a standardized format."""
        analyzer_name = self.__class__.__name__
        timestamp = asyncio.get_event_loop().time()
        if success:
            summary = self._get_result_summary(result)
            print(f"[{analyzer_name}] SUCCESS [{timestamp:.2f}s] - {summary}")
        else:
            print(f"[{analyzer_name}] ERROR [{timestamp:.2f}s] - {str(result)}")

    def clean_json_content(self, content: str) -> str:
        """
        Clean JSON content from markdown code blocks.

        Args:
            content: Content to clean

        Returns:
            Cleaned content
        """
        analyzer_name = self.__class__.__name__

        # Clean the content by removing markdown code blocks if present
        if content.startswith("```json"):
            # Extract JSON content from markdown code block
            content = content.replace("```json", "", 1)
            if "```" in content:
                content = content.split("```")[0]
            content = content.strip()
            print(f"[{analyzer_name}] Cleaned markdown code blocks from content")

        return content

    async def call_model(self, prompt: str) -> Any:
        """
        Call the model with a prompt.

        Args:
            prompt: Prompt to send to the model

        Returns:
            Model response
        """
        analyzer_name = self.__class__.__name__
        try:
            print(f"[{analyzer_name}] Calling model {self.model_name} with provider {self.provider}...")
            print(f"[{analyzer_name}] Prompt length: {len(prompt)} characters")

            start_time = asyncio.get_event_loop().time()
            response = await self.llm_client.generate(
                prompt=prompt,
                temperature=0.3,
                max_tokens=2048
            )
            end_time = asyncio.get_event_loop().time()

            elapsed_time = end_time - start_time
            print(f"[{analyzer_name}] Model response received in {elapsed_time:.2f} seconds")
            print(f"[{analyzer_name}] Response status: {response.status_code}")

            if hasattr(response, 'output') and 'choices' in response.output and len(response.output['choices']) > 0:
                content_length = len(response.output['choices'][0]['message']['content'])
                print(f"[{analyzer_name}] Response content length: {content_length} characters")

            return response
        except Exception as e:
            print(f"[{analyzer_name}] Error calling model: {str(e)}")
            raise
