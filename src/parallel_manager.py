"""
Parallel workflow management for the conversation analysis agent.
"""
import os
import asyncio
import psutil
from typing import List, Dict, Any, Callable, Coroutine, Optional
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm
import time
import json


class ParallelManager:
    """
    Class for managing parallel workflows.
    """

    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize the parallel manager with dynamic worker adjustment based on CPU cores and system load.
        """
        # Load configuration from environment variables
        self.min_workers = int(os.environ.get("MIN_WORKERS", 2))
        max_workers_env = int(os.environ.get("MAX_WORKERS", 8))
        self.cpu_usage_threshold = int(os.environ.get("CPU_USAGE_THRESHOLD", 80))
        
        # Get CPU core count and calculate suggested workers
        cpu_count = os.cpu_count() or 4  # Default to 4 cores if undetectable
        suggested_workers = int(cpu_count * 1.5)  # 1.5 workers per CPU core
        
        # Determine final worker count with boundaries
        if max_workers is None:
            self.max_workers = max(self.min_workers, min(suggested_workers, max_workers_env))
        else:
            self.max_workers = max(self.min_workers, min(max_workers, max_workers_env))
        
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.semaphore = asyncio.Semaphore(self.max_workers)
        print(f"Initialized ParallelManager with {self.max_workers} workers (CPU cores: {cpu_count})")
        
        # Initialize resource monitoring
        self.resource_monitor = {
            "start_time": time.time(),
            "end_time": None,
            "peak_cpu": 0,
            "peak_memory": 0,
            "avg_cpu": [],
            "avg_memory": [],
            "monitoring_active": False,
            "monitor_task": None
        }

    async def process_files_parallel(
        self,
        files: List[str],
        process_func: Callable[[str, str], Coroutine[Any, Any, Dict[str, Any]]],
        output_dir: str
    ) -> List[str]:
        """
        Process files in parallel.

        Args:
            files: List of file paths
            process_func: Function to process each file
            output_dir: Directory to save results

        Returns:
            List of result file paths (None for failed files)
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        result_files = []

        # Sort files by size to prioritize smaller files (task prioritization)
        files.sort(key=lambda x: os.path.getsize(x))

        print(f"Starting to process {len(files)} files with {self.max_workers} workers")

    async def monitor_resources(self):
        """Continuously monitor system resources during processing."""
        self.resource_monitoring_active = True
        while self.resource_monitoring_active:
            # Get current CPU and memory usage
            cpu_usage = psutil.cpu_percent(interval=1)
            memory_usage = psutil.virtual_memory().percent
            
            # Update peak usage
            if cpu_usage > self.resource_monitor["peak_cpu"]:
                self.resource_monitor["peak_cpu"] = cpu_usage
            if memory_usage > self.resource_monitor["peak_memory"]:
                self.resource_monitor["peak_memory"] = memory_usage
            
            # Store for average calculation
            self.resource_monitor["avg_cpu"].append(cpu_usage)
            self.resource_monitor["avg_memory"].append(memory_usage)
            
            await asyncio.sleep(2)  # Check every 2 seconds

    async def process_files_parallel(
        self,
        files: List[str],
        process_func: Callable[[str, str], Coroutine[Any, Any, Dict[str, Any]]],
        output_dir: str
    ) -> List[str]:
        """
        Process files in parallel.

        Args:
            files: List of file paths
            process_func: Function to process each file
            output_dir: Directory to save results

        Returns:
            List of result file paths (None for failed files)
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        result_files = []

        # Sort files by size to prioritize smaller files (task prioritization)
        files.sort(key=lambda x: os.path.getsize(x))

        print(f"Starting to process {len(files)} files with {self.max_workers} workers")

        async def process_with_semaphore(file_path: str) -> str:
            """Process a file with semaphore control and dynamic concurrency adjustment."""

            # Adjust concurrency based on CPU usage
            cpu_usage = psutil.cpu_percent(interval=0.5)
            current_concurrency = self.semaphore._value
            
            # Decrease concurrency if CPU usage is high
            if cpu_usage > self.cpu_usage_threshold and current_concurrency > self.min_workers:
                new_concurrency = max(current_concurrency - 1, self.min_workers)
                self.semaphore = asyncio.Semaphore(new_concurrency)
                print(f"CPU usage high ({cpu_usage}%), reducing concurrency to {new_concurrency}")
            # Increase concurrency if CPU usage is low and we haven't reached max workers
            elif cpu_usage < self.cpu_usage_threshold * 0.7 and current_concurrency < self.max_workers:
                new_concurrency = min(current_concurrency + 1, self.max_workers)
                self.semaphore = asyncio.Semaphore(new_concurrency)
                print(f"CPU usage low ({cpu_usage}%), increasing concurrency to {new_concurrency}")

            async with self.semaphore:
                file_name = os.path.basename(file_path)
                result_file = os.path.join(output_dir, f"{os.path.splitext(file_name)[0]}_result.json")

                print(f"Starting processing file: {file_name}")

                try:
                    # Process the file
                    result = await process_func(file_path, result_file)

                    # Check if processing was successful
                    if result and result.get("status") == "success":
                        print(f"Successfully processed file: {file_name}")
                        return result_file
                    else:
                        error_msg = result.get("error", "Unknown error") if result else "Unknown error"
                        print(f"Failed to process file: {file_name} - Error: {error_msg}")

                        # Ensure the file is marked as failed in All_result.md
                        try:
                            from src.md_aggregator import mark_file_as_failed
                            result_dir = os.path.dirname(result_file)
                            mark_file_as_failed(file_name, error_msg, result_dir)
                            print(f"Marked file {file_name} as failed in All_result.md")
                        except Exception as mark_error:
                            print(f"Error marking file as failed: {str(mark_error)}")

                        print(f"Failed to process file: {file_name}, continuing with next file")

                        return None

                except Exception as e:
                    print(f"Error processing file {file_name}: {str(e)}")
                    import traceback
                    print(f"Traceback: {traceback.format_exc()}")

                    # Ensure the file is marked as failed in All_result.md
                    try:
                        from src.md_aggregator import mark_file_as_failed
                        result_dir = os.path.dirname(result_file)
                        mark_file_as_failed(file_name, str(e), result_dir)
                        print(f"Marked file {file_name} as failed in All_result.md")
                    except Exception as mark_error:
                        print(f"Error marking file as failed: {str(mark_error)}")

                    print(f"Failed to process file: {file_name}, continuing with next file")

                    return None

        # Start resource monitoring
        self.resource_monitor["monitor_task"] = asyncio.create_task(self.monitor_resources())
        
        # Create tasks for all files
        tasks = [process_with_semaphore(file) for file in files]

        # Process files with progress bar
        completed = 0
        successful = 0
        total = len(tasks)
        pbar = tqdm(total=total, desc="Processing files")

        try:
            for task in asyncio.as_completed(tasks):
                try:
                    result_file = await task
                    result_files.append(result_file)
                    completed += 1
                    if result_file:
                        successful += 1
                    pbar.update(1)
                    print(f"Progress: {completed}/{total} files completed ({(completed/total)*100:.1f}%), {successful} successful")

                except asyncio.CancelledError:
                    # Task was cancelled, just continue
                    continue

        except asyncio.CancelledError:
            print("Processing cancelled.")
        finally:
            pbar.close()

        return result_files

    def get_resource_usage_stats(self) -> Dict[str, Any]:
        """Calculate and return resource usage statistics."""
        if not self.resource_monitor["avg_cpu"]:
            return {
                "processing_time": 0,
                "peak_cpu_usage": 0,
                "peak_memory_usage": 0,
                "avg_cpu_usage": 0,
                "avg_memory_usage": 0
            }
            
        return {
            "processing_time": self.resource_monitor["end_time"] - self.resource_monitor["start_time"],
            "peak_cpu_usage": self.resource_monitor["peak_cpu"],
            "peak_memory_usage": self.resource_monitor["peak_memory"],
            "avg_cpu_usage": sum(self.resource_monitor["avg_cpu"]) / len(self.resource_monitor["avg_cpu"]),
            "avg_memory_usage": sum(self.resource_monitor["avg_memory"]) / len(self.resource_monitor["avg_memory"])
        }

    async def close(self):
        """
        Close the executor and stop resource monitoring.
        """
        # Stop resource monitoring
        if self.resource_monitor["monitoring_active"]:
            self.resource_monitoring_active = False
            if self.resource_monitor["monitor_task"]:
                await self.resource_monitor["monitor_task"]
        
        # Record end time
        self.resource_monitor["end_time"] = time.time()
        
        # Get and log resource usage stats
        resource_stats = self.get_resource_usage_stats()
        print("\n=== Resource Usage Statistics ===")
        print(f"Processing time: {resource_stats['processing_time']:.2f} seconds")
        print(f"Peak CPU usage: {resource_stats['peak_cpu_usage']}%")
        print(f"Peak memory usage: {resource_stats['peak_memory_usage']}%")
        print(f"Average CPU usage: {resource_stats['avg_cpu_usage']:.2f}%")
        print(f"Average memory usage: {resource_stats['avg_memory_usage']:.2f}%")
        
        # Save stats to file
        stats_dir = "performance_stats"
        if not os.path.exists(stats_dir):
            os.makedirs(stats_dir)
            
        stats_file = os.path.join(stats_dir, f"stats_{int(time.time())}.json")
        with open(stats_file, "w") as f:
            json.dump(resource_stats, f, indent=2)
        
        self.executor.shutdown()
