"""
Aggregation script for processing All_result.json.

This script reads the All_result.json file, which contains the results of analyzing
multiple conversation files, and performs various aggregation operations.
It can use either the Qwen API or a local model for analysis assistance.
"""
import json
from typing import Dict, Any
from dotenv import load_dotenv
from src.llm_client import LocalLLMClient

# Load environment variables
load_dotenv()

# Constants
INPUT_FILE = "output/All_result.json"

def load_all_results() -> Dict[str, Any]:
    """
    Load the All_result.json file.

    Returns:
        The loaded JSON data
    """
    with open(INPUT_FILE, 'r', encoding='utf-8') as file:
        return json.load(file)





def call_qwen_model(prompt: str) -> str:
    """
    Call the LLM model API based on configuration in .env.

    Args:
        prompt: The prompt to send to the model

    Returns:
        The model's response
    """
    try:
        # Create LLM client using configuration from .env
        client = LocalLLMClient()
        print(f"Using model provider: {client.provider}")
        print(f"Using model: {client.model}")
        print(f"Using API base: {client.api_base}")

        # Create async function to call the model
        async def call_model():
            response = await client.generate(
                prompt=prompt,
                temperature=0.7,
                max_tokens=1000
            )

            if response.status_code == 200:
                if hasattr(response.output, 'choices'):
                    return response.output.choices[0].message.content
                else:
                    return response.output['choices'][0]['message']['content']
            else:
                print(f"Error calling LLM API: {response.status_code}")
                return ""

        # Run the async function
        import asyncio
        return asyncio.run(call_model())
    except Exception as e:
        print(f"Exception when calling LLM API: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return ""

def main():
    """
    Main function to run the aggregation process.
    """
    print("Starting aggregation process...")

    # Load data
    all_results = load_all_results()
    results = all_results.get("results", [])
    print(f"Loaded {len(results)} result files")

    # Process the results as needed
    # This function no longer transforms All_result.json into data.js format

    print("Aggregation completed.")

if __name__ == "__main__":
    main()
