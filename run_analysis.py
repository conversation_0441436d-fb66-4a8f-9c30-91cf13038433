"""
<PERSON><PERSON><PERSON> to run the main analysis and then the aggregation.
"""
import os
import subprocess
import time

def main():
    """
    Run the main analysis and then the aggregation.
    """
    print("Starting analysis...")
    
    # Check if the output directory exists
    if not os.path.exists("output"):
        os.makedirs("output")
    
    # Run the main analysis
    try:
        print("Running main.py...")
        subprocess.run(["python", "main.py"], check=True)
        print("Main analysis completed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error running main.py: {e}")
        return
    
    # Wait a moment
    time.sleep(2)
    
    # Check if All_result.json exists
    if not os.path.exists("output/All_result.json"):
        print("Error: output/All_result.json not found.")
        return
    
    # Run the aggregation
    try:
        print("Running aggregation.py...")
        subprocess.run(["python", "aggregation.py"], check=True)
        print("Aggregation completed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error running aggregation.py: {e}")
        return
    
    print("Analysis and aggregation completed successfully.")

if __name__ == "__main__":
    main()
